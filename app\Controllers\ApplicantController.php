<?php

namespace App\Controllers;

use App\Models\applicantsModel;
use App\Models\ApplicantsExperiencesModel;
use App\Models\ApplicantEducationModel;
use App\Models\EducationModel;
use App\Models\ApplicantFilesModel;

class ApplicantController extends BaseController
{
    protected $applicantsModel;
    protected $experiencesModel;
    protected $applicantEducationModel;
    protected $educationModel;
    protected $applicantFilesModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->applicantsModel = new applicantsModel();
        $this->experiencesModel = new ApplicantsExperiencesModel();
        $this->applicantEducationModel = new ApplicantEducationModel();
        $this->educationModel = new EducationModel();
        $this->applicantFilesModel = new ApplicantFilesModel();
    }

    public function dashboard()
    {
        // Get applicant data
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Initialize empty data for features that require missing models
        $data = [
            'title' => 'Applicant Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant,
            'total_applications' => 0,
            'pending_applications' => 0,
            'shortlisted_applications' => 0,
            'rejected_applications' => 0,
            'recent_applications' => [],
            'latest_jobs' => []
        ];

        return view('applicant/applicant_dashboard', $data);
    }

    public function profile()
    {
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Get work experiences
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                            ->orderBy('date_from', 'DESC')
                                            ->findAll();

        // Get applicant's education records using automatic model features
        $education = $this->applicantEducationModel->where('applicant_id', $applicant_id)
                                                 ->orderBy('date_from', 'DESC')
                                                 ->findAll();

        // Get education levels from adx_education table for dropdowns
        $education_data = $this->educationModel->findAll();

        // Get applicant's files
        $files = $this->applicantFilesModel->where('applicant_id', $applicant_id)
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        $data = [
            'title' => 'Edit Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_data' => $education_data,
            'files' => $files,
            'education_levels' => [
                1 => 'Elementary',
                2 => 'High School',
                3 => 'Vocational',
                4 => 'College',
                5 => 'Post Graduate'
            ]
        ];

        return view('applicant/applicant_profile', $data);
    }

    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');
        $photo = $this->request->getFile('id_photo');

        // Debug response for troubleshooting
        if (!$photo || !$photo->isValid() || $photo->hasMoved()) {
            $debug = [
                'photo_exists' => !empty($photo),
                'is_valid' => $photo ? $photo->isValid() : false,
                'has_moved' => $photo ? $photo->hasMoved() : false,
                'error' => $photo ? $photo->getError() : 'No file uploaded'
            ];

            $response = [
                'success' => false,
                'message' => 'Invalid file upload',
                'debug' => $debug
            ];
            return $this->response->setJSON($response);
        }

        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!in_array($photo->getMimeType(), $allowedTypes)) {
            $response = [
                'success' => false,
                'message' => 'Only JPG, JPEG, and PNG files are allowed',
                'debug' => ['mime_type' => $photo->getMimeType()]
            ];
            return $this->response->setJSON($response);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/photos';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Debug info
            $debug = [
                'upload_path_exists' => is_dir($uploadPath),
                'upload_path_writable' => is_writable($uploadPath),
                'upload_path' => $uploadPath,
                'file_name' => $photo->getName(),
                'file_size' => $photo->getSize(),
                'mime_type' => $photo->getMimeType()
            ];

            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $photo->getRandomName();
            $debug['new_name'] = $newName;

            // Move file to uploads directory
            if ($photo->move($uploadPath, $newName)) {
                // Verify file was created
                $debug['file_created'] = file_exists($uploadPath . '/' . $newName);

                // Delete old photo if exists
                $applicant = $this->applicantsModel->find($applicant_id);
                if (!empty($applicant['id_photo_path'])) {
                    $oldPhotoPath = $applicant['id_photo_path'];
                    $debug['old_photo_path'] = $oldPhotoPath;
                    $debug['old_photo_exists'] = file_exists($oldPhotoPath);

                    if (file_exists(ROOTPATH . $oldPhotoPath)) {
                        unlink(ROOTPATH . $oldPhotoPath);
                        $debug['old_photo_deleted'] = true;
                    }
                }

                // Update database with new photo path - store path WITH 'public/' prefix for easy view retrieval
                $this->applicantsModel->update($applicant_id, [
                    'id_photo_path' => 'public/uploads/photos/' . $newName,
                    'updated_by' => $applicant_id
                ]);

                $debug['db_updated'] = true;
                $debug['path_stored'] = 'public/uploads/photos/' . $newName;

                $response = [
                    'success' => true,
                    'message' => 'Photo updated successfully',
                    'path' => base_url('public/uploads/photos/' . $newName),
                    'debug' => $debug
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Error uploading photo',
                    'debug' => array_merge($debug, [
                        'move_error' => $photo->getError(),
                        'error_code' => $photo->getError()
                    ])
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'message' => 'Error uploading photo: ' . $e->getMessage(),
                'debug' => $debug
            ];
        }

        // Return JSON response
        return $this->response->setJSON($response);
    }

    public function updatePersonal()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'fname' => $this->request->getPost('fname'),
            'lname' => $this->request->getPost('lname'),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => $this->request->getPost('contact_details'),
            'location_address' => $this->request->getPost('location_address'),
            'place_of_origin' => $this->request->getPost('place_of_origin'),
            'citizenship' => $this->request->getPost('citizenship'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Personal information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateDocuments()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Documents updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating documents: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateEmployment()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Employment information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating employment information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateFamily()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage'),
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => json_encode($this->request->getPost('children') ?? []),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Family information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating family information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateAdditional()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Additional information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating additional information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function changePassword()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $current_password = $this->request->getPost('current_password');
        $new_password = $this->request->getPost('new_password');
        $confirm_password = $this->request->getPost('confirm_password');

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        // Verify current password
        if (!password_verify($current_password, $applicant['password'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Current password is incorrect'
            ]);
        }

        // Verify new passwords match
        if ($new_password !== $confirm_password) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New passwords do not match'
            ]);
        }

        try {
            $this->applicantsModel->update($applicant_id, [
                'password' => $new_password,
                'updated_by' => $applicant_id
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error changing password: ' . $e->getMessage()
            ]);
        }
    }

    public function addExperience()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'applicant_id' => $applicant_id,
            'employer' => $this->request->getPost('employer'),
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'achievements' => $this->request->getPost('achievements'),
            'work_description' => $this->request->getPost('work_description'),
            'created_by' => $applicant_id,
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->insert($data);

            // Get the newly added experience with proper date formatting
            $newExperience = $this->experiencesModel->find($this->experiencesModel->getInsertID());

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience added successfully',
                'experience' => $newExperience
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function updateExperience()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Experience not found or access denied'
            ]);
        }

        $data = [
            'employer' => $this->request->getPost('employer'),
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'achievements' => $this->request->getPost('achievements'),
            'work_description' => $this->request->getPost('work_description'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->update($experience_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience updated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteExperience($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Experience not found or access denied'
            ]);
        }

        try {
            $this->experiencesModel->delete($id);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function addEducation()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'applicant_id' => $applicant_id,
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'education_level' => $this->request->getPost('education_level'),
            'units' => $this->request->getPost('units'),
            'created_by' => $applicant_id,
            'updated_by' => $applicant_id
        ];

        try {
            if ($this->applicantEducationModel->insert($data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Education record added successfully'
                ]);
            }
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to add education record'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding education record: ' . $e->getMessage()
            ]);
        }
    }

    public function updateEducation()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->applicantEducationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Education record not found or access denied'
            ]);
        }

        $data = [
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'education_level' => $this->request->getPost('education_level'),
            'units' => $this->request->getPost('units'),
            'updated_by' => $applicant_id
        ];

        try {
            if ($this->applicantEducationModel->update($education_id, $data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Education record updated successfully'
                ]);
            }
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update education record'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating education record: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteEducation($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // For UI development - simulate ownership verification
        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Education record not found or access denied'
            ]);
        }

        // For UI development - simulate successful deletion
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Education record deleted successfully'
        ]);
    }

    public function uploadFile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $file = $this->request->getFile('file');
        $title = $this->request->getPost('file_title');
        $description = $this->request->getPost('file_description');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file upload'
            ]);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/applicant_files';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $file->getRandomName();

            // Move file to uploads directory
            if ($file->move($uploadPath, $newName)) {
                // Save file record to database
                $data = [
                    'applicant_id' => $applicant_id,
                    'file_title' => $title,
                    'file_description' => $description,
                    'file_path' => 'public/uploads/applicant_files/' . $newName,
                    'created_by' => $applicant_id,
                    'updated_by' => $applicant_id
                ];

                // For UI development - simulate successful file upload
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File uploaded successfully'
                ]);
            }

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteFile($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // For UI development - simulate ownership verification
        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found or access denied'
            ]);
        }

        // For UI development - simulate successful file deletion
        return $this->response->setJSON([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    public function viewApplication($applicationId)
    {
        // Get current applicant ID from session
        $applicant_id = session()->get('applicant_id');

        // Using dummy data for UI development
        $application = [
            'id' => $applicationId,
            'applicant_id' => $applicant_id,
            'position_id' => 1,
            'status' => 'pending',
            'applied_date' => '2024-01-15',
            'cover_letter' => 'This is a sample cover letter for the application.'
        ];

        $position = [
            'id' => 1,
            'title' => 'Software Developer',
            'description' => 'Develop and maintain software applications',
            'requirements' => 'Bachelor degree in Computer Science',
            'org_id' => 1,
            'position_group_id' => 1
        ];

        $positionGroup = [
            'id' => 1,
            'name' => 'IT Positions',
            'exercise_id' => 1
        ];

        $organization = [
            'id' => 1,
            'name' => 'Department of ICT',
            'address' => 'Port Moresby, NCD'
        ];

        $exercise = [
            'id' => 1,
            'title' => 'IT Recruitment Exercise 2024',
            'description' => 'Annual recruitment for IT positions'
        ];

        $files = [
            [
                'id' => 1,
                'file_title' => 'Resume',
                'file_description' => 'Updated CV',
                'file_path' => 'public/uploads/applicant_files/resume.pdf'
            ]
        ];

        $experiences = [
            [
                'id' => 1,
                'employer' => 'ABC Company',
                'position' => 'Junior Developer',
                'date_from' => '2020-01-01',
                'date_to' => '2023-12-31',
                'work_description' => 'Developed web applications'
            ]
        ];

        $education = [
            [
                'id' => 1,
                'institution' => 'University of Papua New Guinea',
                'qualification' => 'Bachelor of Computer Science',
                'date_from' => '2016-01-01',
                'date_to' => '2019-12-31',
                'education_level' => 4
            ]
        ];

        $totalExperience = 4;
        $highestEducation = 'Bachelor of Computer Science';

        // Prepare data for the view
        $data = [
            'title' => 'Application Details',
            'menu' => 'applications',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'organization' => $organization,
            'exercise' => $exercise,
            'files' => $files,
            'experiences' => $experiences,
            'education' => $education,
            'totalExperience' => $totalExperience,
            'highestEducation' => $highestEducation,
            'educationLevels' => [
                1 => 'Primary',
                2 => 'Secondary',
                3 => 'Certificate',
                4 => 'Diploma',
                5 => 'Bachelor\'s Degree',
                6 => 'Master\'s Degree',
                7 => 'Doctorate'
            ]
        ];

        // Render the view
        return view('applicant/applicant_application_details', $data);
    }
}